import { Router } from 'express';
import { GeneratedScheduleController } from '@/controllers/generatedScheduleController';
import { authenticate } from '@/middleware/auth';

const router = Router();
const generatedScheduleController = new GeneratedScheduleController();

// All generated schedule routes require authentication
router.use(authenticate);

// Generated schedule CRUD routes
router.post('/', generatedScheduleController.createGeneratedSchedule);
router.get('/', generatedScheduleController.getGeneratedSchedules);
router.get('/:id', generatedScheduleController.getGeneratedScheduleById);
router.delete('/:id', generatedScheduleController.deleteGeneratedSchedule);

export default router;
