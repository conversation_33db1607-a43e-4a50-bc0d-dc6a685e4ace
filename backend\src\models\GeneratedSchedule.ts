import mongoose, { Schema, Document } from 'mongoose';

export interface IGeneratedSchedule extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description?: string;
  timeSlots: Array<{
    teacherId: mongoose.Types.ObjectId;
    classroomId: mongoose.Types.ObjectId;
    levelId: mongoose.Types.ObjectId;
    start: string;
    end: string;
    dayOfWeek: number;
  }>;
  generationMetadata?: {
    parameters: any;
    fitness: number;
    generationsUsed: number;
    executionTime: number;
  };
  userId: mongoose.Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const generatedScheduleSchema = new Schema<IGeneratedSchedule>(
  {
    name: {
      type: String,
      required: [true, 'Schedule name is required'],
      trim: true,
      maxlength: [100, 'Schedule name cannot exceed 100 characters'],
    },
    description: {
      type: String,
      trim: true,
      maxlength: [500, 'Description cannot exceed 500 characters'],
    },
    timeSlots: [{
      teacherId: {
        type: Schema.Types.ObjectId,
        ref: 'Teacher',
        required: true,
      },
      classroomId: {
        type: Schema.Types.ObjectId,
        ref: 'Classroom',
        required: true,
      },
      levelId: {
        type: Schema.Types.ObjectId,
        ref: 'Level',
        required: true,
      },
      start: {
        type: String,
        required: true,
        match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid start time format (HH:MM)'],
      },
      end: {
        type: String,
        required: true,
        match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid end time format (HH:MM)'],
      },
      dayOfWeek: {
        type: Number,
        required: true,
        min: [0, 'Day of week must be between 0 and 6'],
        max: [6, 'Day of week must be between 0 and 6'],
      },
    }],
    generationMetadata: {
      parameters: Schema.Types.Mixed,
      fitness: Number,
      generationsUsed: Number,
      executionTime: Number,
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
  },
  {
    timestamps: true,
    toJSON: {
      transform: function(doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Indexes for better query performance
generatedScheduleSchema.index({ userId: 1, createdAt: -1 });
generatedScheduleSchema.index({ name: 'text', description: 'text' });

// Virtual to get time slot count
generatedScheduleSchema.virtual('timeSlotCount').get(function() {
  return this.timeSlots.length;
});

// Ensure virtual fields are serialized
generatedScheduleSchema.set('toJSON', { virtuals: true });

export const GeneratedSchedule = mongoose.model<IGeneratedSchedule>('GeneratedSchedule', generatedScheduleSchema);
