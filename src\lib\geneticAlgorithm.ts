import { TimeSlot, Teacher, Classroom, Level, AutoGenerationParameters } from './schemas';
import { ConstraintValidator } from './constraints';

export interface Individual {
  timeSlots: TimeSlot[];
  fitness: number;
  id: string;
}

export interface GenerationResult {
  bestIndividual: Individual;
  averageFitness: number;
  generation: number;
  convergenceRate: number;
}

export class GeneticAlgorithm {
  private teachers: Teacher[];
  private classrooms: Classroom[];
  private levels: Level[];
  private parameters: AutoGenerationParameters;
  private validator: ConstraintValidator;
  private availableTimeSlots: string[];

  constructor(
    teachers: Teacher[],
    classrooms: Classroom[],
    levels: Level[],
    parameters: AutoGenerationParameters,
    availableTimeSlots: string[]
  ) {
    this.teachers = teachers;
    this.classrooms = classrooms;
    this.levels = levels;
    this.parameters = parameters;
    this.availableTimeSlots = availableTimeSlots;
    
    this.validator = new ConstraintValidator(
      teachers,
      classrooms,
      levels,
      parameters.teacherPreferences,
      parameters.levelRequirements,
      parameters.constraints
    );
  }

  /**
   * Main genetic algorithm execution
   */
  async evolve(onProgress?: (result: GenerationResult) => void): Promise<Individual> {
    // Initialize population
    let population = this.initializePopulation();
    let bestFitness = 0;
    let stagnationCount = 0;
    const maxStagnation = 20;

    for (let generation = 0; generation < this.parameters.maxGenerations; generation++) {
      // Evaluate fitness for all individuals
      population = this.evaluatePopulation(population);
      
      // Sort by fitness (descending)
      population.sort((a, b) => b.fitness - a.fitness);
      
      const currentBest = population[0];
      const averageFitness = population.reduce((sum, ind) => sum + ind.fitness, 0) / population.length;
      
      // Check for improvement
      if (currentBest.fitness > bestFitness) {
        bestFitness = currentBest.fitness;
        stagnationCount = 0;
      } else {
        stagnationCount++;
      }

      // Report progress
      if (onProgress) {
        onProgress({
          bestIndividual: currentBest,
          averageFitness,
          generation,
          convergenceRate: (generation + 1) / this.parameters.maxGenerations
        });
      }

      // Early termination conditions
      if (currentBest.fitness >= 950 || stagnationCount >= maxStagnation) {
        break;
      }

      // Create next generation
      population = this.createNextGeneration(population);
    }

    // Return best individual
    population = this.evaluatePopulation(population);
    population.sort((a, b) => b.fitness - a.fitness);
    return population[0];
  }

  private initializePopulation(): Individual[] {
    const population: Individual[] = [];

    for (let i = 0; i < this.parameters.populationSize; i++) {
      const individual: Individual = {
        timeSlots: this.generateRandomSchedule(),
        fitness: 0,
        id: `ind_${i}_${Date.now()}`
      };
      population.push(individual);
    }

    return population;
  }

  private generateRandomSchedule(): TimeSlot[] {
    const timeSlots: TimeSlot[] = [];
    
    // Generate slots based on level requirements
    this.parameters.levelRequirements.forEach(levelReq => {
      for (let i = 0; i < levelReq.classesPerWeek; i++) {
        const slot = this.generateRandomTimeSlot(levelReq.levelId, levelReq.classDuration);
        if (slot) {
          timeSlots.push(slot);
        }
      }
    });

    return timeSlots;
  }

  private generateRandomTimeSlot(levelId: string, duration: number): TimeSlot | null {
    const maxAttempts = 50;
    
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      // Random day from working days
      const dayOfWeek = this.parameters.constraints.workingDays[
        Math.floor(Math.random() * this.parameters.constraints.workingDays.length)
      ];

      // Random start time
      const startTime = this.availableTimeSlots[
        Math.floor(Math.random() * this.availableTimeSlots.length)
      ];

      // Calculate end time
      const endTime = this.addMinutesToTime(startTime, duration);

      // Random teacher and classroom
      const teacher = this.teachers[Math.floor(Math.random() * this.teachers.length)];
      const classroom = this.classrooms[Math.floor(Math.random() * this.classrooms.length)];

      const slot: TimeSlot = {
        id: `slot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        teacherId: teacher.id,
        classroomId: classroom.id,
        levelId,
        start: startTime,
        end: endTime,
        dayOfWeek
      };

      // Check if this slot is within working hours
      if (this.isWithinWorkingHours(slot)) {
        return slot;
      }
    }

    return null; // Failed to generate valid slot
  }

  private evaluatePopulation(population: Individual[]): Individual[] {
    return population.map(individual => ({
      ...individual,
      fitness: this.validator.calculateFitnessScore(individual.timeSlots)
    }));
  }

  private createNextGeneration(population: Individual[]): Individual[] {
    const nextGeneration: Individual[] = [];
    const eliteSize = Math.floor(this.parameters.populationSize * 0.1); // Keep top 10%
    
    // Elitism: Keep best individuals
    for (let i = 0; i < eliteSize; i++) {
      nextGeneration.push({
        ...population[i],
        id: `elite_${i}_${Date.now()}`
      });
    }

    // Generate rest through crossover and mutation
    while (nextGeneration.length < this.parameters.populationSize) {
      const parent1 = this.tournamentSelection(population);
      const parent2 = this.tournamentSelection(population);
      
      const offspring = this.crossover(parent1, parent2);
      const mutatedOffspring = this.mutate(offspring);
      
      nextGeneration.push(mutatedOffspring);
    }

    return nextGeneration;
  }

  private tournamentSelection(population: Individual[]): Individual {
    const tournamentSize = 5;
    const tournament: Individual[] = [];

    for (let i = 0; i < tournamentSize; i++) {
      const randomIndex = Math.floor(Math.random() * population.length);
      tournament.push(population[randomIndex]);
    }

    tournament.sort((a, b) => b.fitness - a.fitness);
    return tournament[0];
  }

  private crossover(parent1: Individual, parent2: Individual): Individual {
    const crossoverPoint = Math.floor(Math.random() * Math.min(parent1.timeSlots.length, parent2.timeSlots.length));
    
    const offspring: TimeSlot[] = [
      ...parent1.timeSlots.slice(0, crossoverPoint),
      ...parent2.timeSlots.slice(crossoverPoint)
    ];

    // Remove duplicates and conflicts
    const cleanedOffspring = this.removeDuplicatesAndConflicts(offspring);

    return {
      timeSlots: cleanedOffspring,
      fitness: 0,
      id: `offspring_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  private mutate(individual: Individual): Individual {
    const mutationRate = 0.1;
    const mutatedSlots = [...individual.timeSlots];

    mutatedSlots.forEach((slot, index) => {
      if (Math.random() < mutationRate) {
        // Randomly mutate one aspect of the slot
        const mutationType = Math.floor(Math.random() * 4);
        
        switch (mutationType) {
          case 0: // Change time
            const newStartTime = this.availableTimeSlots[
              Math.floor(Math.random() * this.availableTimeSlots.length)
            ];
            const duration = this.timeToMinutes(slot.end) - this.timeToMinutes(slot.start);
            mutatedSlots[index] = {
              ...slot,
              start: newStartTime,
              end: this.addMinutesToTime(newStartTime, duration)
            };
            break;
            
          case 1: // Change day
            const newDay = this.parameters.constraints.workingDays[
              Math.floor(Math.random() * this.parameters.constraints.workingDays.length)
            ];
            mutatedSlots[index] = { ...slot, dayOfWeek: newDay };
            break;
            
          case 2: // Change teacher
            const newTeacher = this.teachers[Math.floor(Math.random() * this.teachers.length)];
            mutatedSlots[index] = { ...slot, teacherId: newTeacher.id };
            break;
            
          case 3: // Change classroom
            const newClassroom = this.classrooms[Math.floor(Math.random() * this.classrooms.length)];
            mutatedSlots[index] = { ...slot, classroomId: newClassroom.id };
            break;
        }
      }
    });

    return {
      timeSlots: this.removeDuplicatesAndConflicts(mutatedSlots),
      fitness: 0,
      id: `mutated_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  private removeDuplicatesAndConflicts(timeSlots: TimeSlot[]): TimeSlot[] {
    const cleaned: TimeSlot[] = [];
    const usedSlots = new Set<string>();

    timeSlots.forEach(slot => {
      const slotKey = `${slot.teacherId}_${slot.dayOfWeek}_${slot.start}`;
      const classroomKey = `${slot.classroomId}_${slot.dayOfWeek}_${slot.start}`;
      
      if (!usedSlots.has(slotKey) && !usedSlots.has(classroomKey)) {
        // Check if slot is valid
        if (this.isWithinWorkingHours(slot) && !this.conflictsWithBreaks(slot)) {
          cleaned.push(slot);
          usedSlots.add(slotKey);
          usedSlots.add(classroomKey);
        }
      }
    });

    return cleaned;
  }

  private isWithinWorkingHours(slot: TimeSlot): boolean {
    const workStart = this.timeToMinutes(this.parameters.constraints.workingHours.startTime);
    const workEnd = this.timeToMinutes(this.parameters.constraints.workingHours.endTime);
    const slotStart = this.timeToMinutes(slot.start);
    const slotEnd = this.timeToMinutes(slot.end);

    return slotStart >= workStart && slotEnd <= workEnd;
  }

  private conflictsWithBreaks(slot: TimeSlot): boolean {
    return this.parameters.constraints.breakPeriods.some(breakPeriod => {
      if (breakPeriod.dayOfWeek !== undefined && slot.dayOfWeek !== breakPeriod.dayOfWeek) {
        return false;
      }
      return this.timeRangesOverlap(slot.start, slot.end, breakPeriod.startTime, breakPeriod.endTime);
    });
  }

  private timeRangesOverlap(start1: string, end1: string, start2: string, end2: string): boolean {
    const start1Min = this.timeToMinutes(start1);
    const end1Min = this.timeToMinutes(end1);
    const start2Min = this.timeToMinutes(start2);
    const end2Min = this.timeToMinutes(end2);

    return start1Min < end2Min && start2Min < end1Min;
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private addMinutesToTime(time: string, minutes: number): string {
    const totalMinutes = this.timeToMinutes(time) + minutes;
    const hours = Math.floor(totalMinutes / 60);
    const mins = totalMinutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }
}
