import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import { config } from '@/config/environment';
import { connectDatabase } from '@/config/database';
import { seedInitialData } from '@/utils/seedData';
import { errorHandler } from '@/middleware/errorHandler';
import { notFoundHandler } from '@/middleware/notFoundHandler';

// Import routes
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/users';
import scheduleRoutes from '@/routes/scheduleRoutes';
import taskRoutes from '@/routes/tasks';
import teacherRoutes from '@/routes/teachers';
import classroomRoutes from '@/routes/classrooms';
import levelRoutes from '@/routes/levels';
import timeslotRoutes from '@/routes/timeslots';
import scheduleDataRoutes from '@/routes/schedule';
import generatedScheduleRoutes from '@/routes/generatedSchedules';

class App {
  public app: express.Application;

  constructor() {
    this.app = express();
    this.initializeDatabase();
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private async initializeDatabase(): Promise<void> {
    try {
      await connectDatabase();
      console.log('✅ Database connected successfully');

      // Seed initial data
      await seedInitialData();
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      process.exit(1);
    }
  }

  private initializeMiddlewares(): void {
    // Security middleware
    this.app.use(helmet({
      crossOriginResourcePolicy: { policy: "cross-origin" }
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.rateLimitWindowMs,
      max: config.rateLimitMaxRequests,
      message: {
        error: 'Too many requests from this IP, please try again later.'
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // CORS configuration - Allow all origins for development
    this.app.use(cors({
      origin: true,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Compression middleware
    this.app.use(compression());

    // Logging middleware
    if (config.nodeEnv !== 'test') {
      this.app.use(morgan('combined'));
    }

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    this.app.use(cookieParser());

    // Trust proxy for rate limiting behind reverse proxy
    this.app.set('trust proxy', 1);
  }

  private initializeRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (_req, res) => {
      res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: config.nodeEnv,
      });
    });

    // API routes
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/users', userRoutes);
    this.app.use('/api/schedules', scheduleRoutes);
    this.app.use('/api/generated-schedules', generatedScheduleRoutes);
    this.app.use('/api/tasks', taskRoutes);
    this.app.use('/api/teachers', teacherRoutes);
    this.app.use('/api/classrooms', classroomRoutes);
    this.app.use('/api/levels', levelRoutes);
    this.app.use('/api/timeslots', timeslotRoutes);
    this.app.use('/api/schedule', scheduleDataRoutes);

    // API documentation endpoint
    this.app.get('/api', (_req, res) => {
      res.json({
        message: 'Weekly Schedule API',
        version: '1.0.0',
        endpoints: {
          auth: '/api/auth',
          users: '/api/users',
          schedules: '/api/schedules',
          tasks: '/api/tasks',
          teachers: '/api/teachers',
          classrooms: '/api/classrooms',
          levels: '/api/levels',
          timeslots: '/api/timeslots',
        },
        documentation: '/api/docs',
      });
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  public listen(): void {
    this.app.listen(config.port, () => {
      console.log(`🚀 Server running on port ${config.port}`);
      console.log(`📱 Environment: ${config.nodeEnv}`);
      console.log(`🌐 Frontend URL: ${config.frontendUrl}`);
    });
  }
}

export default App;
