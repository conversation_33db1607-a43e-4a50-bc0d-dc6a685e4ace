import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useStore } from '@/store/useStore';
import { TimeSlot } from '@/lib/schemas';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TimePicker } from '@/components/ui/time-picker';
import { Plus, X, Edit, Clock } from 'lucide-react';
import { TimeSlotModal } from './TimeSlotModal';
import { useConfirmDialog } from './ConfirmDialog';
import { toast } from '@/hooks/use-toast';

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

export const TimetableGrid: React.FC = () => {
  const {
    getFilteredTimeSlots,
    teachers,
    classrooms,
    levels,
    deleteTimeSlotAsync,
    selectedTeacher,
    selectedClassroom,
    availableTimeSlots,
    addAvailableTimeSlot,
    removeAvailableTimeSlot,
  } = useStore();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [selectedCell, setSelectedCell] = useState<{ day: number; time: string; slots: TimeSlot[] } | null>(null);
  const [newTimeSlot, setNewTimeSlot] = useState('');

  const { showConfirm, ConfirmDialog } = useConfirmDialog();

  const timeSlots = getFilteredTimeSlots();
  
  console.log('Current time slots:', timeSlots);
  console.log('Teachers:', teachers);
  console.log('Classrooms:', classrooms);
  console.log('Levels:', levels);

  const getSlotsForCell = (day: number, time: string) => {
    const slots = timeSlots.filter((slot) => {
      const slotStartTime = slot.start;
      const slotEndTime = slot.end;
      return slot.dayOfWeek === day && time >= slotStartTime && time < slotEndTime;
    });
    console.log(`Slots for day ${day} time ${time}:`, slots);
    return slots;
  };

  const handleCellClick = (day: number, time: string, existingSlot?: TimeSlot) => {
    console.log('Cell clicked:', { day, time, existingSlot });
    
    if (existingSlot) {
      setSelectedSlot(existingSlot);
      setSelectedCell(null);
    } else {
      const slotsInCell = getSlotsForCell(day, time);
      setSelectedCell({ day, time, slots: slotsInCell });
      setSelectedSlot(null);
    }
    setIsModalOpen(true);
  };

  const handleDeleteSlot = async (slot: TimeSlot, e: React.MouseEvent) => {
    e.stopPropagation();

    const teacher = teachers.find(t => t.id === slot.teacherId);
    const classroom = classrooms.find(c => c.id === slot.classroomId);
    const level = levels.find(l => l.id === slot.levelId);

    const slotDescription = `${teacher?.name || 'Unknown Teacher'} - ${classroom?.name || 'Unknown Classroom'} - ${level?.label || 'Unknown Level'}`;

    showConfirm({
      title: 'Delete Time Slot',
      description: `Are you sure you want to delete this time slot?\n\n${slotDescription}\n${slot.start} - ${slot.end} on ${DAYS[slot.dayOfWeek]}\n\nThis action cannot be undone.`,
      confirmText: 'Delete Time Slot',
      variant: 'destructive',
      onConfirm: async () => {
        console.log('Deleting slot:', slot.id);
        await deleteTimeSlotAsync(slot.id);
      }
    });
  };

  const handleAddTimeSlot = () => {
    if (!newTimeSlot.trim()) {
      toast({
        title: "Invalid Time",
        description: "Please select a time.",
        variant: "destructive",
      });
      return;
    }

    if (availableTimeSlots.includes(newTimeSlot)) {
      toast({
        title: "Time Slot Exists",
        description: "This time slot already exists.",
        variant: "destructive",
      });
      return;
    }

    addAvailableTimeSlot(newTimeSlot);
    setNewTimeSlot('');
    toast({
      title: "Time Slot Added",
      description: `Time slot ${newTimeSlot} has been added.`,
    });
  };

  const handleRemoveTimeSlot = (timeSlot: string) => {
    removeAvailableTimeSlot(timeSlot);
    toast({
      title: "Time Slot Removed",
      description: `Time slot ${timeSlot} has been removed.`,
    });
  };

  const getTeacherById = (id: string) => teachers.find(t => t.id === id);
  const getClassroomById = (id: string) => classrooms.find(c => c.id === id);
  const getLevelById = (id: string) => levels.find(l => l.id === id);

  const renderTimeSlot = (slot: TimeSlot, isStacked: boolean = false) => {
    const teacher = getTeacherById(slot.teacherId);
    const classroom = getClassroomById(slot.classroomId);
    const level = getLevelById(slot.levelId);

    console.log('Rendering slot:', { slot, teacher, classroom, level });

    if (!teacher || !classroom || !level) {
      console.log('Missing data for slot:', { teacher: !!teacher, classroom: !!classroom, level: !!level });
      return null;
    }

    return (
      <motion.div
        key={slot.id}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.8 }}
        className={`glass-card p-2 ${isStacked ? 'mb-1' : 'm-1'} relative group cursor-pointer shine-effect`}
        style={{ backgroundColor: `${teacher.color}20`, borderColor: teacher.color }}
        onClick={() => handleCellClick(slot.dayOfWeek, slot.start, slot)}
      >
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-1 right-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={(e) => handleDeleteSlot(slot, e)}
        >
          <X className="h-3 w-3" />
        </Button>
        
        <div className="text-xs font-medium text-gray-900 mb-1">{teacher.name}</div>
        <div className="text-xs text-gray-700 mb-1">{classroom.name}</div>
        <Badge 
          variant="secondary" 
          className="text-xs"
          style={{ backgroundColor: level.color, color: 'white' }}
        >
          {level.label}
        </Badge>
        {!isStacked && (
          <div className="text-xs text-gray-600 mt-1">
            {slot.start} - {slot.end}
          </div>
        )}
      </motion.div>
    );
  };

  return (
    <div className="glass-card p-6 liquid-container">
      <div className="liquid-background absolute inset-0 opacity-30"></div>
      
      <div className="relative z-10">
        <div className="mb-6">
          <h2 className="text-2xl font-bold mb-2">School Timetable</h2>
          <p className="text-sm text-gray-600 mb-4">Click on any cell to add a new time slot. Multiple teachers and classes can be scheduled in the same time period.</p>

          {/* Time Slot Management */}
          <div className="glass-card p-4 mb-4">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Manage Time Slots
            </h3>
            <div className="flex gap-2 mb-3">
              <div className="flex-1">
                <TimePicker
                  value={newTimeSlot}
                  onChange={setNewTimeSlot}
                  placeholder="Select time slot"
                  className="w-full"
                />
              </div>
              <Button onClick={handleAddTimeSlot} size="sm">
                <Plus className="h-4 w-4 mr-1" />
                Add
              </Button>
            </div>

            {availableTimeSlots.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {availableTimeSlots.map((timeSlot) => (
                  <Badge
                    key={timeSlot}
                    variant="secondary"
                    className="flex items-center gap-1 px-3 py-1"
                  >
                    {timeSlot}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1 hover:bg-red-100"
                      onClick={() => handleRemoveTimeSlot(timeSlot)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500 italic">No time slots defined. Add some time slots to start creating your schedule.</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-8 gap-2 mb-4">
          <div className="font-semibold text-center p-2"></div>
          {DAYS.map((day, index) => (
            <div key={day} className="font-semibold text-center p-2 glass-card">
              {day}
            </div>
          ))}
        </div>

        {availableTimeSlots.length > 0 ? (
          <div className="grid grid-cols-8 gap-2 max-h-[600px] overflow-y-auto">
            {availableTimeSlots.map((time) => (
              <React.Fragment key={time}>
                <div className="text-sm text-center p-2 glass-card font-medium">
                  {time}
                </div>
                {DAYS.map((_, dayIndex) => {
                  const slots = getSlotsForCell(dayIndex, time);
                  return (
                    <div
                      key={`${dayIndex}-${time}`}
                      className="min-h-[60px] border border-gray-200 rounded-lg relative hover:bg-glass-light transition-all cursor-pointer bg-white"
                      onClick={() => handleCellClick(dayIndex, time)}
                    >
                      <AnimatePresence>
                        {slots.length > 0 ? (
                          <div className="h-full overflow-y-auto p-1">
                            {slots.map((slot, index) =>
                              renderTimeSlot(slot, slots.length > 1)
                            )}
                          </div>
                        ) : (
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            className="w-full h-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity"
                          >
                            <Plus className="h-4 w-4 text-gray-400" />
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  );
                })}
              </React.Fragment>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Clock className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-600 mb-2">No Time Slots Available</h3>
            <p className="text-gray-500">Add some time slots above to start building your schedule.</p>
          </div>
        )}
      </div>

      <TimeSlotModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedSlot(null);
          setSelectedCell(null);
        }}
        existingSlot={selectedSlot}
        existingSlots={selectedCell?.slots}
        defaultDay={selectedCell?.day}
        defaultTime={selectedCell?.time}
      />

      <ConfirmDialog />
    </div>
  );
};
