
import { z } from 'zod';

export const TeacherSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Name is required'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
});

export const ClassroomSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Name is required'),
  capacity: z.number().min(1, 'Capacity must be at least 1'),
});

export const LevelSchema = z.object({
  id: z.string(),
  label: z.string().min(1, 'Label is required'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
});

export const TimeSlotSchema = z.object({
  id: z.string(),
  teacherId: z.string(),
  classroomId: z.string(),
  levelId: z.string(),
  start: z.string(),
  end: z.string(),
  dayOfWeek: z.number().min(0).max(6),
});

export type Teacher = z.infer<typeof TeacherSchema>;
export type Classroom = z.infer<typeof ClassroomSchema>;
export type Level = z.infer<typeof LevelSchema>;
export type TimeSlot = z.infer<typeof TimeSlotSchema>;

// Auto Generation Schemas
export const TeacherPreferenceSchema = z.object({
  teacherId: z.string(),
  maxClassesPerDay: z.number().min(1).max(10).default(6),
  preferredDays: z.array(z.number().min(0).max(6)).default([1, 2, 3, 4, 5]),
  preferredTimeSlots: z.array(z.string()).default([]),
  minBreakBetweenClasses: z.number().min(0).max(120).default(15), // minutes
  unavailableSlots: z.array(z.object({
    dayOfWeek: z.number().min(0).max(6),
    startTime: z.string(),
    endTime: z.string(),
  })).default([]),
});

export const LevelRequirementSchema = z.object({
  levelId: z.string(),
  classesPerWeek: z.number().min(1).max(30).default(5),
  preferredTimeDistribution: z.enum(['morning', 'afternoon', 'balanced']).default('balanced'),
  priority: z.enum(['low', 'medium', 'high']).default('medium'),
  classDuration: z.number().min(30).max(180).default(60), // minutes
  requiresSpecialRoom: z.boolean().default(false),
  specialRoomType: z.string().optional(),
});

export const GenerationConstraintsSchema = z.object({
  workingHours: z.object({
    startTime: z.string().default('08:00'),
    endTime: z.string().default('17:00'),
  }),
  breakPeriods: z.array(z.object({
    name: z.string(),
    startTime: z.string(),
    endTime: z.string(),
    dayOfWeek: z.number().min(0).max(6).optional(), // if undefined, applies to all days
  })).default([
    { name: 'Lunch Break', startTime: '12:00', endTime: '13:00' }
  ]),
  minClassDuration: z.number().min(15).max(60).default(30),
  maxClassDuration: z.number().min(60).max(240).default(120),
  maxConsecutiveClasses: z.number().min(1).max(8).default(4),
  workingDays: z.array(z.number().min(0).max(6)).default([1, 2, 3, 4, 5]),
});

export const OptimizationGoalsSchema = z.object({
  minimizeTeacherConflicts: z.number().min(0).max(10).default(10),
  maximizeClassroomUtilization: z.number().min(0).max(10).default(7),
  balanceWorkloadDistribution: z.number().min(0).max(10).default(8),
  minimizeGapsInSchedules: z.number().min(0).max(10).default(6),
  respectTeacherPreferences: z.number().min(0).max(10).default(9),
  optimizeTimeDistribution: z.number().min(0).max(10).default(5),
});

export const AutoGenerationParametersSchema = z.object({
  teacherPreferences: z.array(TeacherPreferenceSchema).default([]),
  levelRequirements: z.array(LevelRequirementSchema).default([]),
  constraints: GenerationConstraintsSchema.default({}),
  optimizationGoals: OptimizationGoalsSchema.default({}),
  preserveExistingSlots: z.boolean().default(false),
  maxGenerationAttempts: z.number().min(1).max(100).default(50),
  populationSize: z.number().min(10).max(200).default(50),
  maxGenerations: z.number().min(10).max(1000).default(100),
});

export type TeacherPreference = z.infer<typeof TeacherPreferenceSchema>;
export type LevelRequirement = z.infer<typeof LevelRequirementSchema>;
export type GenerationConstraints = z.infer<typeof GenerationConstraintsSchema>;
export type OptimizationGoals = z.infer<typeof OptimizationGoalsSchema>;
export type AutoGenerationParameters = z.infer<typeof AutoGenerationParametersSchema>;
