
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useStore } from '@/store/useStore';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Plus, Users, MapPin, BookOpen, Filter, Download, RefreshCw, Database, Zap, Trash2 } from 'lucide-react';
import { EntityModal } from './EntityModal';
import { ExportModal } from './ExportModal';
import { ConnectionStatus } from './ConnectionStatus';
import { useConfirmDialog } from './ConfirmDialog';
import { useNavigate } from 'react-router-dom';

export const Sidebar: React.FC = () => {
  const {
    teachers,
    classrooms,
    levels,
    timeSlots,
    selectedTeacher,
    selectedClassroom,
    setSelectedTeacher,
    setSelectedClassroom,
    resetToDefaults,
    testApiConnection,
    deleteTeacherAsync,
    deleteClassroomAsync,
    deleteLevelAsync,
  } = useStore();

  const navigate = useNavigate();
  const [entityModalOpen, setEntityModalOpen] = useState(false);
  const [entityType, setEntityType] = useState<'teacher' | 'classroom' | 'level'>('teacher');
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const { showConfirm, ConfirmDialog } = useConfirmDialog();

  const openEntityModal = (type: 'teacher' | 'classroom' | 'level') => {
    setEntityType(type);
    setEntityModalOpen(true);
  };

  const handleDeleteTeacher = async (id: string, name: string, e: React.MouseEvent) => {
    e.stopPropagation();
    showConfirm({
      title: 'Delete Teacher',
      description: `Are you sure you want to delete teacher "${name}"? This will also remove all their scheduled time slots. This action cannot be undone.`,
      confirmText: 'Delete Teacher',
      variant: 'destructive',
      onConfirm: async () => {
        try {
          await deleteTeacherAsync(id);
          // Clear selection if the deleted teacher was selected
          if (selectedTeacher === id) {
            setSelectedTeacher(null);
          }
        } catch (error) {
          // Error handling is done by the async function
          console.error('Error deleting teacher:', error);
        }
      }
    });
  };

  const handleDeleteClassroom = async (id: string, name: string, e: React.MouseEvent) => {
    e.stopPropagation();
    showConfirm({
      title: 'Delete Classroom',
      description: `Are you sure you want to delete classroom "${name}"? This will also remove all scheduled time slots in this classroom. This action cannot be undone.`,
      confirmText: 'Delete Classroom',
      variant: 'destructive',
      onConfirm: async () => {
        try {
          await deleteClassroomAsync(id);
          // Clear selection if the deleted classroom was selected
          if (selectedClassroom === id) {
            setSelectedClassroom(null);
          }
        } catch (error) {
          // Error handling is done by the async function
          console.error('Error deleting classroom:', error);
        }
      }
    });
  };

  const handleDeleteLevel = async (id: string, label: string, e: React.MouseEvent) => {
    e.stopPropagation();
    showConfirm({
      title: 'Delete Level',
      description: `Are you sure you want to delete level "${label}"? This will also remove all scheduled time slots for this level. This action cannot be undone.`,
      confirmText: 'Delete Level',
      variant: 'destructive',
      onConfirm: async () => {
        try {
          await deleteLevelAsync(id);
        } catch (error) {
          // Error handling is done by the async function
          console.error('Error deleting level:', error);
        }
      }
    });
  };

  return (
    <motion.div
      initial={{ x: -300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      className="w-80 h-full glass-card p-6 liquid-container"
    >
      <div className="liquid-background absolute inset-0 opacity-20"></div>
      
      <div className="relative z-10 h-full flex flex-col">
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              School Timetable
            </h1>
            <ConnectionStatus />
          </div>
          <p className="text-sm text-muted-foreground">Admin Dashboard</p>
        </div>

        <ScrollArea className="flex-1">
          {/* Filters */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <Filter className="h-4 w-4 mr-2" />
              <h3 className="font-semibold">Filters</h3>
            </div>
            <div className="space-y-2">
              <Button
                variant={selectedTeacher ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedTeacher(selectedTeacher ? null : teachers[0]?.id)}
                className="w-full glass-button justify-start"
              >
                Filter by Teacher
              </Button>
              <Button
                variant={selectedClassroom ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedClassroom(selectedClassroom ? null : classrooms[0]?.id)}
                className="w-full glass-button justify-start"
              >
                Filter by Classroom
              </Button>
            </div>
          </div>

          <Separator className="my-4" />

          {/* Teachers */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                <h3 className="font-semibold">Teachers</h3>
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => openEntityModal('teacher')}
                className="h-6 w-6 p-0 glass-button"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
            <div className="space-y-2">
              {teachers.map((teacher) => (
                <motion.div
                  key={teacher.id}
                  whileHover={{ scale: 1.02 }}
                  className={`group p-2 rounded-lg cursor-pointer transition-all shine-effect ${
                    selectedTeacher === teacher.id
                      ? 'bg-glass-light border border-white/30'
                      : 'hover:bg-glass-dark'
                  }`}
                  onClick={() => setSelectedTeacher(selectedTeacher === teacher.id ? null : teacher.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: teacher.color }}
                      />
                      <span className="text-sm font-medium">{teacher.name}</span>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => handleDeleteTeacher(teacher.id, teacher.name, e)}
                      className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-500/20 hover:text-red-500 transition-all"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Classrooms */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                <h3 className="font-semibold">Classrooms</h3>
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => openEntityModal('classroom')}
                className="h-6 w-6 p-0 glass-button"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
            <div className="space-y-2">
              {classrooms.map((classroom) => (
                <motion.div
                  key={classroom.id}
                  whileHover={{ scale: 1.02 }}
                  className={`group p-2 rounded-lg cursor-pointer transition-all shine-effect ${
                    selectedClassroom === classroom.id
                      ? 'bg-glass-light border border-white/30'
                      : 'hover:bg-glass-dark'
                  }`}
                  onClick={() => setSelectedClassroom(selectedClassroom === classroom.id ? null : classroom.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">{classroom.name}</span>
                      <Badge variant="secondary" className="text-xs">
                        {classroom.capacity}
                      </Badge>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => handleDeleteClassroom(classroom.id, classroom.name, e)}
                      className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-500/20 hover:text-red-500 transition-all"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Levels */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <BookOpen className="h-4 w-4 mr-2" />
                <h3 className="font-semibold">Levels</h3>
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => openEntityModal('level')}
                className="h-6 w-6 p-0 glass-button"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
            <div className="space-y-2">
              {levels.map((level) => (
                <motion.div
                  key={level.id}
                  whileHover={{ scale: 1.02 }}
                  className="group p-2 rounded-lg hover:bg-glass-dark transition-all shine-effect"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: level.color }}
                      />
                      <span className="text-sm font-medium">{level.label}</span>
                    </div>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={(e) => handleDeleteLevel(level.id, level.label, e)}
                      className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-red-500/20 hover:text-red-500 transition-all"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
          {/* Data Status & Debug */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <Database className="h-4 w-4 mr-2" />
              <h3 className="font-semibold">Data Status</h3>
            </div>
            <div className="space-y-2">
              <div className="text-xs text-gray-600 space-y-1">
                <div>Teachers: {teachers.length}</div>
                <div>Classrooms: {classrooms.length}</div>
                <div>Levels: {levels.length}</div>
                <div>Time Slots: {timeSlots.length}</div>
              </div>
              <Button
                onClick={resetToDefaults}
                size="sm"
                variant="outline"
                className="w-full glass-button text-xs mb-2"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Reset to Defaults
              </Button>
              <Button
                onClick={testApiConnection}
                size="sm"
                variant="outline"
                className="w-full glass-button text-xs"
              >
                <Database className="h-3 w-3 mr-1" />
                Test API
              </Button>
            </div>
          </div>
        </ScrollArea>

        {/* Action Buttons */}
        <div className="mt-4 space-y-3">
          <Button
            onClick={() => navigate('/auto-generate')}
            className="w-full glass-button bg-gradient-to-r from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30"
          >
            <Zap className="h-4 w-4 mr-2" />
            Auto Generate
          </Button>

          <Button
            onClick={() => setExportModalOpen(true)}
            className="w-full glass-button bg-gradient-to-r from-blue-500/20 to-purple-500/20 hover:from-blue-500/30 hover:to-purple-500/30"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Timetable
          </Button>
        </div>
      </div>

      <EntityModal
        isOpen={entityModalOpen}
        onClose={() => setEntityModalOpen(false)}
        entityType={entityType}
      />

      <ExportModal
        isOpen={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
      />

      <ConfirmDialog />
    </motion.div>
  );
};
