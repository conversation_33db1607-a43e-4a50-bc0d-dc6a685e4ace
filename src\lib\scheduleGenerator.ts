import { TimeSlot, Teacher, Classroom, Level, AutoGenerationParameters } from './schemas';
import { ConstraintValidator, ScheduleConflict } from './constraints';
import { GeneticAlgorithm, Individual, GenerationResult } from './geneticAlgorithm';

export interface GenerationProgress {
  phase: 'initializing' | 'generating' | 'optimizing' | 'validating' | 'completed' | 'error';
  progress: number; // 0-100
  message: string;
  currentGeneration?: number;
  bestFitness?: number;
  averageFitness?: number;
  conflicts?: ScheduleConflict[];
  estimatedTimeRemaining?: number;
}

export interface GenerationResult {
  success: boolean;
  timeSlots: TimeSlot[];
  conflicts: ScheduleConflict[];
  fitness: number;
  generationsUsed: number;
  executionTime: number;
  message: string;
}

export class ScheduleGenerator {
  private teachers: Teacher[];
  private classrooms: Classroom[];
  private levels: Level[];
  private availableTimeSlots: string[];
  private validator: ConstraintValidator | null = null;

  constructor(
    teachers: Teacher[],
    classrooms: Classroom[],
    levels: Level[],
    availableTimeSlots: string[]
  ) {
    this.teachers = teachers;
    this.classrooms = classrooms;
    this.levels = levels;
    this.availableTimeSlots = availableTimeSlots;
  }

  /**
   * Generate a complete schedule using the provided parameters
   */
  async generateSchedule(
    parameters: AutoGenerationParameters,
    existingTimeSlots: TimeSlot[] = [],
    onProgress?: (progress: GenerationProgress) => void
  ): Promise<GenerationResult> {
    const startTime = Date.now();
    let generationsUsed = 0;

    try {
      // Phase 1: Initialize and validate
      if (onProgress) {
        onProgress({
          phase: 'initializing',
          progress: 0,
          message: 'Initializing schedule generator...'
        });
      }

      // Validate input parameters
      const validationResult = this.validateParameters(parameters);
      if (!validationResult.valid) {
        return {
          success: false,
          timeSlots: [],
          conflicts: [],
          fitness: 0,
          generationsUsed: 0,
          executionTime: Date.now() - startTime,
          message: `Parameter validation failed: ${validationResult.errors.join(', ')}`
        };
      }

      // Initialize constraint validator
      this.validator = new ConstraintValidator(
        this.teachers,
        this.classrooms,
        this.levels,
        parameters.teacherPreferences,
        parameters.levelRequirements,
        parameters.constraints
      );

      // Phase 2: Generate initial schedule
      if (onProgress) {
        onProgress({
          phase: 'generating',
          progress: 10,
          message: 'Generating initial schedule...'
        });
      }

      // Prepare base schedule (preserve existing if requested)
      let baseSchedule: TimeSlot[] = [];
      if (parameters.preserveExistingSlots && existingTimeSlots.length > 0) {
        baseSchedule = [...existingTimeSlots];
        if (onProgress) {
          onProgress({
            phase: 'generating',
            progress: 20,
            message: `Preserving ${existingTimeSlots.length} existing time slots...`
          });
        }
      }

      // Phase 3: Genetic Algorithm Optimization
      if (onProgress) {
        onProgress({
          phase: 'optimizing',
          progress: 30,
          message: 'Starting genetic algorithm optimization...'
        });
      }

      const geneticAlgorithm = new GeneticAlgorithm(
        this.teachers,
        this.classrooms,
        this.levels,
        parameters,
        this.availableTimeSlots
      );

      let bestIndividual: Individual;

      try {
        bestIndividual = await geneticAlgorithm.evolve((result: GenerationResult) => {
          generationsUsed = result.generation + 1;
          
          if (onProgress) {
            const progress = 30 + (result.convergenceRate * 60); // 30-90%
            onProgress({
              phase: 'optimizing',
              progress,
              message: `Generation ${result.generation + 1}/${parameters.maxGenerations}`,
              currentGeneration: result.generation + 1,
              bestFitness: result.bestIndividual.fitness,
              averageFitness: result.averageFitness,
              estimatedTimeRemaining: this.estimateTimeRemaining(
                startTime,
                result.convergenceRate,
                Date.now()
              )
            });
          }
        });
      } catch (error) {
        return {
          success: false,
          timeSlots: baseSchedule,
          conflicts: [],
          fitness: 0,
          generationsUsed,
          executionTime: Date.now() - startTime,
          message: `Genetic algorithm failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
      }

      // Phase 4: Validation and conflict resolution
      if (onProgress) {
        onProgress({
          phase: 'validating',
          progress: 90,
          message: 'Validating generated schedule...'
        });
      }

      const finalSchedule = [...baseSchedule, ...bestIndividual.timeSlots];
      const conflicts = this.validator.validateSchedule(finalSchedule);
      const finalFitness = this.validator.calculateFitnessScore(finalSchedule);

      // Phase 5: Post-processing
      const optimizedSchedule = this.postProcessSchedule(finalSchedule, conflicts);
      const finalConflicts = this.validator.validateSchedule(optimizedSchedule);

      if (onProgress) {
        onProgress({
          phase: 'completed',
          progress: 100,
          message: `Schedule generated successfully with ${finalConflicts.length} conflicts`,
          conflicts: finalConflicts
        });
      }

      const executionTime = Date.now() - startTime;

      return {
        success: true,
        timeSlots: optimizedSchedule,
        conflicts: finalConflicts,
        fitness: finalFitness,
        generationsUsed,
        executionTime,
        message: this.generateSummaryMessage(optimizedSchedule, finalConflicts, generationsUsed, executionTime)
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      if (onProgress) {
        onProgress({
          phase: 'error',
          progress: 0,
          message: `Generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }

      return {
        success: false,
        timeSlots: existingTimeSlots,
        conflicts: [],
        fitness: 0,
        generationsUsed,
        executionTime,
        message: `Schedule generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Validate generation parameters
   */
  private validateParameters(parameters: AutoGenerationParameters): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check if we have required entities
    if (this.teachers.length === 0) {
      errors.push('No teachers available');
    }

    if (this.classrooms.length === 0) {
      errors.push('No classrooms available');
    }

    if (this.levels.length === 0) {
      errors.push('No levels available');
    }

    // Check level requirements
    if (parameters.levelRequirements.length === 0) {
      errors.push('No level requirements specified');
    }

    // Validate level requirements reference existing levels
    parameters.levelRequirements.forEach(req => {
      if (!this.levels.find(l => l.id === req.levelId)) {
        errors.push(`Level requirement references non-existent level: ${req.levelId}`);
      }
    });

    // Validate teacher preferences reference existing teachers
    parameters.teacherPreferences.forEach(pref => {
      if (!this.teachers.find(t => t.id === pref.teacherId)) {
        errors.push(`Teacher preference references non-existent teacher: ${pref.teacherId}`);
      }
    });

    // Check working hours
    const workStart = this.timeToMinutes(parameters.constraints.workingHours.startTime);
    const workEnd = this.timeToMinutes(parameters.constraints.workingHours.endTime);
    
    if (workStart >= workEnd) {
      errors.push('Working hours end time must be after start time');
    }

    // Check if there's enough time in the week for all required classes
    const totalRequiredClasses = parameters.levelRequirements.reduce(
      (sum, req) => sum + req.classesPerWeek, 0
    );
    
    const workingMinutesPerDay = workEnd - workStart;
    const totalWorkingMinutes = workingMinutesPerDay * parameters.constraints.workingDays.length;
    const avgClassDuration = parameters.levelRequirements.reduce(
      (sum, req) => sum + req.classDuration, 0
    ) / parameters.levelRequirements.length;
    
    const requiredMinutes = totalRequiredClasses * avgClassDuration;
    
    if (requiredMinutes > totalWorkingMinutes * 0.8) { // 80% utilization threshold
      errors.push('Not enough time in the week for all required classes');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Post-process schedule to resolve minor conflicts
   */
  private postProcessSchedule(timeSlots: TimeSlot[], conflicts: ScheduleConflict[]): TimeSlot[] {
    let optimizedSlots = [...timeSlots];

    // Try to resolve critical conflicts
    const criticalConflicts = conflicts.filter(c => c.severity === 'critical');
    
    criticalConflicts.forEach(conflict => {
      if (conflict.type === 'teacher_conflict' || conflict.type === 'classroom_conflict') {
        // Try to reschedule one of the conflicting slots
        const conflictingSlots = optimizedSlots.filter(slot => 
          conflict.affectedSlots.includes(slot.id)
        );

        if (conflictingSlots.length >= 2) {
          // Try to move the second slot to a different time
          const slotToMove = conflictingSlots[1];
          const newSlot = this.findAlternativeSlot(slotToMove, optimizedSlots);
          
          if (newSlot) {
            optimizedSlots = optimizedSlots.map(slot => 
              slot.id === slotToMove.id ? newSlot : slot
            );
          }
        }
      }
    });

    return optimizedSlots;
  }

  /**
   * Find an alternative time slot for a given slot
   */
  private findAlternativeSlot(originalSlot: TimeSlot, existingSlots: TimeSlot[]): TimeSlot | null {
    const duration = this.timeToMinutes(originalSlot.end) - this.timeToMinutes(originalSlot.start);

    // Try different times on the same day first
    for (const timeSlot of this.availableTimeSlots) {
      const endTime = this.addMinutesToTime(timeSlot, duration);
      
      const testSlot: TimeSlot = {
        ...originalSlot,
        start: timeSlot,
        end: endTime
      };

      if (this.validator && this.validator.canAddTimeSlot(testSlot, existingSlots.filter(s => s.id !== originalSlot.id))) {
        return testSlot;
      }
    }

    // Try different days
    const workingDays = [1, 2, 3, 4, 5]; // Monday to Friday
    for (const day of workingDays) {
      if (day === originalSlot.dayOfWeek) continue;

      for (const timeSlot of this.availableTimeSlots) {
        const endTime = this.addMinutesToTime(timeSlot, duration);
        
        const testSlot: TimeSlot = {
          ...originalSlot,
          dayOfWeek: day,
          start: timeSlot,
          end: endTime
        };

        if (this.validator && this.validator.canAddTimeSlot(testSlot, existingSlots.filter(s => s.id !== originalSlot.id))) {
          return testSlot;
        }
      }
    }

    return null;
  }

  private generateSummaryMessage(
    timeSlots: TimeSlot[],
    conflicts: ScheduleConflict[],
    generations: number,
    executionTime: number
  ): string {
    const criticalConflicts = conflicts.filter(c => c.severity === 'critical').length;
    const highConflicts = conflicts.filter(c => c.severity === 'high').length;
    
    let message = `Generated ${timeSlots.length} time slots in ${generations} generations (${(executionTime / 1000).toFixed(1)}s). `;
    
    if (criticalConflicts === 0 && highConflicts === 0) {
      message += 'Schedule is optimal with no major conflicts.';
    } else if (criticalConflicts === 0) {
      message += `Schedule has ${highConflicts} high-priority conflicts that should be reviewed.`;
    } else {
      message += `Schedule has ${criticalConflicts} critical conflicts that must be resolved.`;
    }

    return message;
  }

  private estimateTimeRemaining(startTime: number, progress: number, currentTime: number): number {
    if (progress <= 0) return 0;
    
    const elapsed = currentTime - startTime;
    const totalEstimated = elapsed / progress;
    return Math.max(0, totalEstimated - elapsed);
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private addMinutesToTime(time: string, minutes: number): string {
    const totalMinutes = this.timeToMinutes(time) + minutes;
    const hours = Math.floor(totalMinutes / 60);
    const mins = totalMinutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }
}
