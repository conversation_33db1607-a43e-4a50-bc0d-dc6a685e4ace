import { Request, Response, NextFunction } from 'express';
import { GeneratedSchedule } from '@/models/GeneratedSchedule';
import { Teacher } from '@/models/Teacher';
import { Classroom } from '@/models/Classroom';
import { Level } from '@/models/Level';
import { AppError } from '@/utils/AppError';
import { IAuthRequest } from '@/types';

export class GeneratedScheduleController {
  // Create new generated schedule
  public async createGeneratedSchedule(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { name, description, timeSlots, generationMetadata } = req.body;

      // Validate that all referenced entities exist
      const teacherIds = [...new Set(timeSlots.map((slot: any) => slot.teacherId))];
      const classroomIds = [...new Set(timeSlots.map((slot: any) => slot.classroomId))];
      const levelIds = [...new Set(timeSlots.map((slot: any) => slot.levelId))];

      const [teachers, classrooms, levels] = await Promise.all([
        Teacher.find({ _id: { $in: teacherIds } }),
        Classroom.find({ _id: { $in: classroomIds } }),
        Level.find({ _id: { $in: levelIds } }),
      ]);

      if (teachers.length !== teacherIds.length) {
        throw new AppError('One or more teachers not found', 404);
      }
      if (classrooms.length !== classroomIds.length) {
        throw new AppError('One or more classrooms not found', 404);
      }
      if (levels.length !== levelIds.length) {
        throw new AppError('One or more levels not found', 404);
      }

      const generatedSchedule = new GeneratedSchedule({
        name,
        description,
        timeSlots,
        generationMetadata,
        userId: user._id,
      });

      await generatedSchedule.save();

      res.status(201).json({
        success: true,
        message: 'Generated schedule saved successfully',
        data: {
          id: generatedSchedule._id,
          name: generatedSchedule.name,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get all user's generated schedules
  public async getGeneratedSchedules(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { page = 1, limit = 10, search } = req.query;

      // Build query
      const query: any = { userId: user._id };
      
      if (search) {
        query.$text = { $search: search };
      }

      const skip = (Number(page) - 1) * Number(limit);

      const [schedules, total] = await Promise.all([
        GeneratedSchedule.find(query)
          .select('name description createdAt timeSlots')
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(Number(limit)),
        GeneratedSchedule.countDocuments(query),
      ]);

      // Transform to include timeSlotCount
      const transformedSchedules = schedules.map(schedule => ({
        id: schedule._id,
        name: schedule.name,
        description: schedule.description,
        createdAt: schedule.createdAt,
        timeSlotCount: schedule.timeSlots.length,
      }));

      res.json({
        success: true,
        message: 'Generated schedules retrieved successfully',
        data: {
          schedules: transformedSchedules,
          pagination: {
            page: Number(page),
            limit: Number(limit),
            total,
            pages: Math.ceil(total / Number(limit)),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Get generated schedule by ID
  public async getGeneratedScheduleById(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { id } = req.params;

      const schedule = await GeneratedSchedule.findOne({ _id: id, userId: user._id })
        .populate([
          { path: 'timeSlots.teacherId', select: 'name color' },
          { path: 'timeSlots.classroomId', select: 'name capacity' },
          { path: 'timeSlots.levelId', select: 'label color' }
        ]);

      if (!schedule) {
        throw new AppError('Generated schedule not found', 404);
      }

      // Transform time slots to match frontend format
      const transformedTimeSlots = schedule.timeSlots.map((slot: any) => ({
        id: slot._id,
        teacherId: slot.teacherId._id,
        classroomId: slot.classroomId._id,
        levelId: slot.levelId._id,
        start: slot.start,
        end: slot.end,
        dayOfWeek: slot.dayOfWeek,
      }));

      res.json({
        success: true,
        message: 'Generated schedule retrieved successfully',
        data: {
          id: schedule._id,
          name: schedule.name,
          description: schedule.description,
          timeSlots: transformedTimeSlots,
          createdAt: schedule.createdAt,
          generationMetadata: schedule.generationMetadata,
        },
      });
    } catch (error) {
      next(error);
    }
  }

  // Delete generated schedule
  public async deleteGeneratedSchedule(req: IAuthRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = req.user!;
      const { id } = req.params;

      const schedule = await GeneratedSchedule.findOneAndDelete({ _id: id, userId: user._id });

      if (!schedule) {
        throw new AppError('Generated schedule not found', 404);
      }

      res.json({
        success: true,
        message: 'Generated schedule deleted successfully',
      });
    } catch (error) {
      next(error);
    }
  }
}
