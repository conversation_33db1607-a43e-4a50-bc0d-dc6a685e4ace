import { TimeSlot, Teacher, Classroom, Level, TeacherPreference, LevelRequirement, GenerationConstraints } from './schemas';

export interface ScheduleConflict {
  type: 'teacher_conflict' | 'classroom_conflict' | 'time_conflict' | 'preference_violation' | 'constraint_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedSlots: string[];
  suggestions?: string[];
}

export class ConstraintValidator {
  private teachers: Teacher[];
  private classrooms: Classroom[];
  private levels: Level[];
  private teacherPreferences: TeacherPreference[];
  private levelRequirements: LevelRequirement[];
  private constraints: GenerationConstraints;

  constructor(
    teachers: Teacher[],
    classrooms: Classroom[],
    levels: Level[],
    teacherPreferences: TeacherPreference[],
    levelRequirements: LevelRequirement[],
    constraints: GenerationConstraints
  ) {
    this.teachers = teachers;
    this.classrooms = classrooms;
    this.levels = levels;
    this.teacherPreferences = teacherPreferences;
    this.levelRequirements = levelRequirements;
    this.constraints = constraints;
  }

  /**
   * Validates a complete schedule and returns all conflicts
   */
  validateSchedule(timeSlots: TimeSlot[]): ScheduleConflict[] {
    const conflicts: ScheduleConflict[] = [];

    // Check for basic conflicts
    conflicts.push(...this.checkTeacherConflicts(timeSlots));
    conflicts.push(...this.checkClassroomConflicts(timeSlots));
    conflicts.push(...this.checkTimeConstraints(timeSlots));
    conflicts.push(...this.checkTeacherPreferences(timeSlots));
    conflicts.push(...this.checkLevelRequirements(timeSlots));
    conflicts.push(...this.checkWorkingHours(timeSlots));
    conflicts.push(...this.checkBreakPeriods(timeSlots));

    return conflicts;
  }

  /**
   * Checks if a new time slot can be added without conflicts
   */
  canAddTimeSlot(newSlot: TimeSlot, existingSlots: TimeSlot[]): boolean {
    const testSlots = [...existingSlots, newSlot];
    const conflicts = this.validateSchedule(testSlots);
    
    // Only allow if there are no critical conflicts
    return !conflicts.some(conflict => conflict.severity === 'critical');
  }

  /**
   * Gets the fitness score for a schedule (higher is better)
   */
  calculateFitnessScore(timeSlots: TimeSlot[]): number {
    const conflicts = this.validateSchedule(timeSlots);
    let score = 1000; // Start with perfect score

    // Deduct points based on conflict severity
    conflicts.forEach(conflict => {
      switch (conflict.severity) {
        case 'critical':
          score -= 100;
          break;
        case 'high':
          score -= 50;
          break;
        case 'medium':
          score -= 20;
          break;
        case 'low':
          score -= 5;
          break;
      }
    });

    // Add bonus points for good distribution
    score += this.calculateDistributionBonus(timeSlots);
    score += this.calculateUtilizationBonus(timeSlots);

    return Math.max(0, score);
  }

  private checkTeacherConflicts(timeSlots: TimeSlot[]): ScheduleConflict[] {
    const conflicts: ScheduleConflict[] = [];
    const teacherSlots = new Map<string, TimeSlot[]>();

    // Group slots by teacher
    timeSlots.forEach(slot => {
      if (!teacherSlots.has(slot.teacherId)) {
        teacherSlots.set(slot.teacherId, []);
      }
      teacherSlots.get(slot.teacherId)!.push(slot);
    });

    // Check for overlapping slots for each teacher
    teacherSlots.forEach((slots, teacherId) => {
      for (let i = 0; i < slots.length; i++) {
        for (let j = i + 1; j < slots.length; j++) {
          if (this.slotsOverlap(slots[i], slots[j])) {
            const teacher = this.teachers.find(t => t.id === teacherId);
            conflicts.push({
              type: 'teacher_conflict',
              severity: 'critical',
              description: `Teacher ${teacher?.name || teacherId} has overlapping classes`,
              affectedSlots: [slots[i].id, slots[j].id],
              suggestions: ['Reschedule one of the conflicting classes', 'Assign different teacher']
            });
          }
        }
      }
    });

    return conflicts;
  }

  private checkClassroomConflicts(timeSlots: TimeSlot[]): ScheduleConflict[] {
    const conflicts: ScheduleConflict[] = [];
    const classroomSlots = new Map<string, TimeSlot[]>();

    // Group slots by classroom
    timeSlots.forEach(slot => {
      if (!classroomSlots.has(slot.classroomId)) {
        classroomSlots.set(slot.classroomId, []);
      }
      classroomSlots.get(slot.classroomId)!.push(slot);
    });

    // Check for overlapping slots for each classroom
    classroomSlots.forEach((slots, classroomId) => {
      for (let i = 0; i < slots.length; i++) {
        for (let j = i + 1; j < slots.length; j++) {
          if (this.slotsOverlap(slots[i], slots[j])) {
            const classroom = this.classrooms.find(c => c.id === classroomId);
            conflicts.push({
              type: 'classroom_conflict',
              severity: 'critical',
              description: `Classroom ${classroom?.name || classroomId} has overlapping bookings`,
              affectedSlots: [slots[i].id, slots[j].id],
              suggestions: ['Reschedule one of the conflicting classes', 'Assign different classroom']
            });
          }
        }
      }
    });

    return conflicts;
  }

  private checkTimeConstraints(timeSlots: TimeSlot[]): ScheduleConflict[] {
    const conflicts: ScheduleConflict[] = [];

    timeSlots.forEach(slot => {
      const startMinutes = this.timeToMinutes(slot.start);
      const endMinutes = this.timeToMinutes(slot.end);
      const duration = endMinutes - startMinutes;

      // Check minimum duration
      if (duration < this.constraints.minClassDuration) {
        conflicts.push({
          type: 'constraint_violation',
          severity: 'medium',
          description: `Class duration (${duration}min) is below minimum (${this.constraints.minClassDuration}min)`,
          affectedSlots: [slot.id],
          suggestions: ['Extend class duration', 'Combine with another short class']
        });
      }

      // Check maximum duration
      if (duration > this.constraints.maxClassDuration) {
        conflicts.push({
          type: 'constraint_violation',
          severity: 'medium',
          description: `Class duration (${duration}min) exceeds maximum (${this.constraints.maxClassDuration}min)`,
          affectedSlots: [slot.id],
          suggestions: ['Split into shorter classes', 'Add break in the middle']
        });
      }

      // Check if day is a working day
      if (!this.constraints.workingDays.includes(slot.dayOfWeek)) {
        conflicts.push({
          type: 'constraint_violation',
          severity: 'high',
          description: `Class scheduled on non-working day`,
          affectedSlots: [slot.id],
          suggestions: ['Move to a working day']
        });
      }
    });

    return conflicts;
  }

  private checkTeacherPreferences(timeSlots: TimeSlot[]): ScheduleConflict[] {
    const conflicts: ScheduleConflict[] = [];

    this.teacherPreferences.forEach(pref => {
      const teacherSlots = timeSlots.filter(slot => slot.teacherId === pref.teacherId);
      const teacher = this.teachers.find(t => t.id === pref.teacherId);

      // Check max classes per day
      const slotsByDay = new Map<number, TimeSlot[]>();
      teacherSlots.forEach(slot => {
        if (!slotsByDay.has(slot.dayOfWeek)) {
          slotsByDay.set(slot.dayOfWeek, []);
        }
        slotsByDay.get(slot.dayOfWeek)!.push(slot);
      });

      slotsByDay.forEach((daySlots, day) => {
        if (daySlots.length > pref.maxClassesPerDay) {
          conflicts.push({
            type: 'preference_violation',
            severity: 'medium',
            description: `Teacher ${teacher?.name || pref.teacherId} has ${daySlots.length} classes on day ${day}, exceeding preference of ${pref.maxClassesPerDay}`,
            affectedSlots: daySlots.map(s => s.id),
            suggestions: ['Redistribute classes across other days', 'Increase max classes per day preference']
          });
        }
      });

      // Check preferred days
      teacherSlots.forEach(slot => {
        if (!pref.preferredDays.includes(slot.dayOfWeek)) {
          conflicts.push({
            type: 'preference_violation',
            severity: 'low',
            description: `Teacher ${teacher?.name || pref.teacherId} has class on non-preferred day ${slot.dayOfWeek}`,
            affectedSlots: [slot.id],
            suggestions: ['Move to preferred day if possible']
          });
        }
      });

      // Check unavailable slots
      pref.unavailableSlots.forEach(unavailable => {
        teacherSlots.forEach(slot => {
          if (slot.dayOfWeek === unavailable.dayOfWeek && 
              this.timeRangesOverlap(slot.start, slot.end, unavailable.startTime, unavailable.endTime)) {
            conflicts.push({
              type: 'preference_violation',
              severity: 'high',
              description: `Teacher ${teacher?.name || pref.teacherId} has class during unavailable time`,
              affectedSlots: [slot.id],
              suggestions: ['Reschedule to available time']
            });
          }
        });
      });
    });

    return conflicts;
  }

  private checkLevelRequirements(timeSlots: TimeSlot[]): ScheduleConflict[] {
    const conflicts: ScheduleConflict[] = [];

    this.levelRequirements.forEach(req => {
      const levelSlots = timeSlots.filter(slot => slot.levelId === req.levelId);
      const level = this.levels.find(l => l.id === req.levelId);

      // Check classes per week
      if (levelSlots.length !== req.classesPerWeek) {
        const severity = Math.abs(levelSlots.length - req.classesPerWeek) > 2 ? 'high' : 'medium';
        conflicts.push({
          type: 'constraint_violation',
          severity,
          description: `Level ${level?.label || req.levelId} has ${levelSlots.length} classes, requires ${req.classesPerWeek}`,
          affectedSlots: levelSlots.map(s => s.id),
          suggestions: levelSlots.length < req.classesPerWeek ? 
            ['Add more classes for this level'] : 
            ['Remove excess classes or split into different weeks']
        });
      }

      // Check class duration
      levelSlots.forEach(slot => {
        const duration = this.timeToMinutes(slot.end) - this.timeToMinutes(slot.start);
        if (Math.abs(duration - req.classDuration) > 15) { // 15 minute tolerance
          conflicts.push({
            type: 'constraint_violation',
            severity: 'low',
            description: `Level ${level?.label || req.levelId} class duration (${duration}min) differs from requirement (${req.classDuration}min)`,
            affectedSlots: [slot.id],
            suggestions: ['Adjust class duration to match requirement']
          });
        }
      });
    });

    return conflicts;
  }

  private checkWorkingHours(timeSlots: TimeSlot[]): ScheduleConflict[] {
    const conflicts: ScheduleConflict[] = [];
    const workStart = this.timeToMinutes(this.constraints.workingHours.startTime);
    const workEnd = this.timeToMinutes(this.constraints.workingHours.endTime);

    timeSlots.forEach(slot => {
      const slotStart = this.timeToMinutes(slot.start);
      const slotEnd = this.timeToMinutes(slot.end);

      if (slotStart < workStart || slotEnd > workEnd) {
        conflicts.push({
          type: 'constraint_violation',
          severity: 'high',
          description: `Class scheduled outside working hours (${this.constraints.workingHours.startTime}-${this.constraints.workingHours.endTime})`,
          affectedSlots: [slot.id],
          suggestions: ['Move class to working hours']
        });
      }
    });

    return conflicts;
  }

  private checkBreakPeriods(timeSlots: TimeSlot[]): ScheduleConflict[] {
    const conflicts: ScheduleConflict[] = [];

    this.constraints.breakPeriods.forEach(breakPeriod => {
      timeSlots.forEach(slot => {
        // Check if break applies to this day
        if (breakPeriod.dayOfWeek !== undefined && slot.dayOfWeek !== breakPeriod.dayOfWeek) {
          return;
        }

        if (this.timeRangesOverlap(slot.start, slot.end, breakPeriod.startTime, breakPeriod.endTime)) {
          conflicts.push({
            type: 'constraint_violation',
            severity: 'medium',
            description: `Class overlaps with ${breakPeriod.name} (${breakPeriod.startTime}-${breakPeriod.endTime})`,
            affectedSlots: [slot.id],
            suggestions: ['Reschedule to avoid break period']
          });
        }
      });
    });

    return conflicts;
  }

  private calculateDistributionBonus(timeSlots: TimeSlot[]): number {
    // Bonus for even distribution of classes across days and times
    let bonus = 0;
    
    // Calculate distribution across days
    const dayDistribution = new Map<number, number>();
    timeSlots.forEach(slot => {
      dayDistribution.set(slot.dayOfWeek, (dayDistribution.get(slot.dayOfWeek) || 0) + 1);
    });

    // Bonus for balanced day distribution
    const avgClassesPerDay = timeSlots.length / this.constraints.workingDays.length;
    let dayVariance = 0;
    this.constraints.workingDays.forEach(day => {
      const dayClasses = dayDistribution.get(day) || 0;
      dayVariance += Math.pow(dayClasses - avgClassesPerDay, 2);
    });
    
    bonus += Math.max(0, 50 - dayVariance); // Lower variance = higher bonus

    return bonus;
  }

  private calculateUtilizationBonus(timeSlots: TimeSlot[]): number {
    // Bonus for good classroom and teacher utilization
    let bonus = 0;

    // Teacher utilization bonus
    const teacherUsage = new Map<string, number>();
    timeSlots.forEach(slot => {
      teacherUsage.set(slot.teacherId, (teacherUsage.get(slot.teacherId) || 0) + 1);
    });

    const avgTeacherUsage = timeSlots.length / this.teachers.length;
    let teacherVariance = 0;
    this.teachers.forEach(teacher => {
      const usage = teacherUsage.get(teacher.id) || 0;
      teacherVariance += Math.pow(usage - avgTeacherUsage, 2);
    });

    bonus += Math.max(0, 30 - teacherVariance / 2);

    return bonus;
  }

  private slotsOverlap(slot1: TimeSlot, slot2: TimeSlot): boolean {
    if (slot1.dayOfWeek !== slot2.dayOfWeek) return false;
    return this.timeRangesOverlap(slot1.start, slot1.end, slot2.start, slot2.end);
  }

  private timeRangesOverlap(start1: string, end1: string, start2: string, end2: string): boolean {
    const start1Min = this.timeToMinutes(start1);
    const end1Min = this.timeToMinutes(end1);
    const start2Min = this.timeToMinutes(start2);
    const end2Min = this.timeToMinutes(end2);

    return start1Min < end2Min && start2Min < end1Min;
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }
}
