import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useStore } from '@/store/useStore';
import { TimeSlot, TimeSlotSchema } from '@/lib/schemas';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { notify } from '@/lib/notifications';
import { Edit, Plus } from 'lucide-react';

interface TimeSlotModalProps {
  isOpen: boolean;
  onClose: () => void;
  existingSlot?: TimeSlot | null;
  existingSlots?: TimeSlot[];
  defaultDay?: number;
  defaultTime?: string;
}

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

export const TimeSlotModal: React.FC<TimeSlotModalProps> = ({
  isOpen,
  onClose,
  existingSlot,
  existingSlots = [],
  defaultDay,
  defaultTime,
}) => {
  const { teachers, classrooms, levels, createTimeSlotAsync, updateTimeSlotAsync, checkConflict, checkConflictExcluding } = useStore();
  
  const [mode, setMode] = useState<'choose' | 'add' | 'edit'>('choose');
  const [selectedSlotToEdit, setSelectedSlotToEdit] = useState<TimeSlot | null>(null);
  
  const [formData, setFormData] = useState({
    teacherId: '',
    classroomId: '',
    levelId: '',
    start: '',
    end: '',
    dayOfWeek: 0,
  });

  useEffect(() => {
    if (existingSlot) {
      // Direct edit mode
      setMode('edit');
      setSelectedSlotToEdit(existingSlot);
      setFormData({
        teacherId: existingSlot.teacherId,
        classroomId: existingSlot.classroomId,
        levelId: existingSlot.levelId,
        start: existingSlot.start,
        end: existingSlot.end,
        dayOfWeek: existingSlot.dayOfWeek,
      });
    } else if (existingSlots.length > 0) {
      // Choose mode - existing slots in cell
      setMode('choose');
    } else if (defaultDay !== undefined && defaultTime) {
      // Add mode - empty cell
      setMode('add');
      setFormData({
        teacherId: '',
        classroomId: '',
        levelId: '',
        start: defaultTime,
        end: '',
        dayOfWeek: defaultDay,
      });
    }
  }, [existingSlot, existingSlots, defaultDay, defaultTime]);

  const handleModeChoice = (chosenMode: 'add' | 'edit', slotToEdit?: TimeSlot) => {
    setMode(chosenMode);
    if (chosenMode === 'edit' && slotToEdit) {
      setSelectedSlotToEdit(slotToEdit);
      setFormData({
        teacherId: slotToEdit.teacherId,
        classroomId: slotToEdit.classroomId,
        levelId: slotToEdit.levelId,
        start: slotToEdit.start,
        end: slotToEdit.end,
        dayOfWeek: slotToEdit.dayOfWeek,
      });
    } else if (chosenMode === 'add') {
      const firstSlot = existingSlots[0];
      setFormData({
        teacherId: '',
        classroomId: '',
        levelId: '',
        start: firstSlot?.start || defaultTime || '',
        end: firstSlot?.end || '',
        dayOfWeek: firstSlot?.dayOfWeek || defaultDay || 0,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.teacherId || !formData.classroomId || !formData.levelId || !formData.start || !formData.end) {
      notify.warning("Missing required fields", "Please fill in all fields before creating the time slot.");
      return;
    }

    // Validate that selected entities still exist
    const selectedTeacher = teachers.find(t => t.id === formData.teacherId);
    const selectedClassroom = classrooms.find(c => c.id === formData.classroomId);
    const selectedLevel = levels.find(l => l.id === formData.levelId);

    if (!selectedTeacher) {
      notify.warning("Invalid teacher", "The selected teacher no longer exists. Please select a different teacher.");
      return;
    }
    if (!selectedClassroom) {
      notify.warning("Invalid classroom", "The selected classroom no longer exists. Please select a different classroom.");
      return;
    }
    if (!selectedLevel) {
      notify.warning("Invalid level", "The selected level no longer exists. Please select a different level.");
      return;
    }

    // Validate time format and range
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(formData.start) || !timeRegex.test(formData.end)) {
      notify.warning("Invalid time format", "Please use HH:MM format (e.g., 09:30).");
      return;
    }

    // Convert times to minutes for proper comparison
    const startMinutes = parseInt(formData.start.split(':')[0]) * 60 + parseInt(formData.start.split(':')[1]);
    const endMinutes = parseInt(formData.end.split(':')[0]) * 60 + parseInt(formData.end.split(':')[1]);

    if (endMinutes <= startMinutes) {
      notify.warning("Invalid time range", "End time must be after start time.");
      return;
    }

    // Check for minimum duration (15 minutes)
    if (endMinutes - startMinutes < 15) {
      notify.warning("Invalid duration", "Time slot must be at least 15 minutes long.");
      return;
    }

    // Check for maximum duration (8 hours = 480 minutes)
    if (endMinutes - startMinutes > 480) {
      notify.warning("Invalid duration", "Time slot cannot be longer than 8 hours.");
      return;
    }

    try {
      TimeSlotSchema.parse({
        id: selectedSlotToEdit?.id || 'temp',
        ...formData,
      });

      // Check for conflicts (exclude current slot when editing)
      const hasConflict = selectedSlotToEdit
        ? checkConflictExcluding(formData, selectedSlotToEdit.id)
        : checkConflict(formData);

      if (hasConflict) {
        notify.warning(
          "Schedule conflict detected",
          "This teacher or classroom is already scheduled at this time. Choose a different teacher/classroom or time slot."
        );
        return;
      }

      if (selectedSlotToEdit) {
        await updateTimeSlotAsync(selectedSlotToEdit.id, formData);
      } else {
        await createTimeSlotAsync(formData);
      }

      onClose();
    } catch (error) {
      console.error('Validation error:', error);
      notify.warning("Validation error", "Please check all fields and try again.");
    }
  };

  const getTeacherById = (id: string) => teachers.find(t => t.id === id);
  const getClassroomById = (id: string) => classrooms.find(c => c.id === id);
  const getLevelById = (id: string) => levels.find(l => l.id === id);

  const renderChoiceMode = () => (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">This time slot has existing classes</h3>
        <p className="text-sm text-gray-600 mb-4">Choose what you'd like to do:</p>
      </div>

      <div className="space-y-3">
        <Button
          onClick={() => handleModeChoice('add')}
          className="w-full glass-button flex items-center justify-center space-x-2"
          variant="outline"
        >
          <Plus className="h-4 w-4" />
          <span>Add New Teacher/Class to this time slot</span>
        </Button>

        <div className="border-t pt-3">
          <p className="text-sm text-gray-600 mb-3">Or edit an existing class:</p>
          <div className="space-y-2">
            {existingSlots.map((slot) => {
              const teacher = getTeacherById(slot.teacherId);
              const classroom = getClassroomById(slot.classroomId);
              const level = getLevelById(slot.levelId);
              
              return (
                <Button
                  key={slot.id}
                  onClick={() => handleModeChoice('edit', slot)}
                  className="w-full glass-button flex items-center justify-between p-3 h-auto"
                  variant="outline"
                >
                  <div className="flex items-center space-x-2">
                    <Edit className="h-4 w-4" />
                    <div className="text-left">
                      <div className="font-medium">{teacher?.name}</div>
                      <div className="text-sm text-gray-600">{classroom?.name}</div>
                    </div>
                  </div>
                  <Badge 
                    style={{ backgroundColor: level?.color, color: 'white' }}
                    className="text-xs"
                  >
                    {level?.label}
                  </Badge>
                </Button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );

  const renderForm = () => {
    // Check if we have the required data
    if (teachers.length === 0 || classrooms.length === 0 || levels.length === 0) {
      return (
        <div className="space-y-4 text-center">
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="text-lg font-semibold text-yellow-800 mb-2">Missing Required Data</h3>
            <p className="text-yellow-700 mb-4">
              To create time slots, you need to have teachers, classrooms, and levels configured.
            </p>
            <div className="text-sm text-yellow-600">
              <p>Missing:</p>
              <ul className="list-disc list-inside mt-1">
                {teachers.length === 0 && <li>Teachers</li>}
                {classrooms.length === 0 && <li>Classrooms</li>}
                {levels.length === 0 && <li>Levels</li>}
              </ul>
            </div>
          </div>
          <Button onClick={onClose} className="glass-button">
            Close
          </Button>
        </div>
      );
    }

    return (
      <motion.form
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        onSubmit={handleSubmit}
        className="space-y-4"
      >
        <div className="space-y-2">
          <Label htmlFor="teacher">Teacher</Label>
          <Select
            value={formData.teacherId}
            onValueChange={(value) => setFormData({ ...formData, teacherId: value })}
          >
            <SelectTrigger className="glass-button">
              <SelectValue placeholder="Select a teacher" />
            </SelectTrigger>
            <SelectContent>
              {teachers.map((teacher) => (
                <SelectItem key={teacher.id} value={teacher.id}>
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: teacher.color }}
                    />
                    <span>{teacher.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

      <div className="space-y-2">
        <Label htmlFor="classroom">Classroom</Label>
        <Select
          value={formData.classroomId}
          onValueChange={(value) => setFormData({ ...formData, classroomId: value })}
        >
          <SelectTrigger className="glass-button">
            <SelectValue placeholder="Select a classroom" />
          </SelectTrigger>
          <SelectContent>
            {classrooms.map((classroom) => (
              <SelectItem key={classroom.id} value={classroom.id}>
                {classroom.name} (Capacity: {classroom.capacity})
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="level">Level</Label>
        <Select
          value={formData.levelId}
          onValueChange={(value) => setFormData({ ...formData, levelId: value })}
        >
          <SelectTrigger className="glass-button">
            <SelectValue placeholder="Select a level" />
          </SelectTrigger>
          <SelectContent>
            {levels.map((level) => (
              <SelectItem key={level.id} value={level.id}>
                <div className="flex items-center space-x-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: level.color }}
                  />
                  <span>{level.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="day">Day of Week</Label>
        <Select
          value={formData.dayOfWeek.toString()}
          onValueChange={(value) => setFormData({ ...formData, dayOfWeek: parseInt(value) })}
        >
          <SelectTrigger className="glass-button">
            <SelectValue placeholder="Select a day" />
          </SelectTrigger>
          <SelectContent>
            {DAYS.map((day, index) => (
              <SelectItem key={index} value={index.toString()}>
                {day}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="start">Start Time</Label>
          <Input
            id="start"
            type="time"
            value={formData.start}
            onChange={(e) => setFormData({ ...formData, start: e.target.value })}
            className="glass-button"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="end">End Time</Label>
          <Input
            id="end"
            type="time"
            value={formData.end}
            onChange={(e) => setFormData({ ...formData, end: e.target.value })}
            className="glass-button"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onClose} className="glass-button">
          Cancel
        </Button>
        <Button type="submit" className="glass-button bg-primary/20 hover:bg-primary/30">
          {selectedSlotToEdit ? 'Update' : 'Create'}
        </Button>
      </div>
    </motion.form>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="glass-card border-white/20">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            {mode === 'choose' 
              ? 'Manage Time Slot' 
              : selectedSlotToEdit 
                ? 'Edit Time Slot' 
                : 'Create Time Slot'
            }
          </DialogTitle>
        </DialogHeader>

        {mode === 'choose' ? renderChoiceMode() : renderForm()}
      </DialogContent>
    </Dialog>
  );
};
